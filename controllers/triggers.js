const { triggerService } = require("../service/triggers");
const logsService = require("../service/logs");
const { devicesService } = require("../service/devices");
const { config } = require("../environment/index");
const { asyncHand<PERSON>, getDayTimeRange } = require("../utils/helpers");
const { trackersService } = require("../service/trackers");
const { getUTCOffsetValue } = require("../service/userProfile");
const { log: logger } = require("../utils/logger");
const { getCGMScoreDetails, generateCGMDetailsResponse, BASELINE_DURATION } = require("../utils/cgmScoring");
const targetService = require("../service/targets");

const Ajv = require("ajv");
const addFormats = require("ajv-formats");

const ajv = new Ajv({ allErrors: true });
addFormats(ajv);
const schema = require("../models/triggers.json");
const { getSignedURL } = require("../utils/aws");

const validate = ajv.compile(schema);

const mealLogTrackerId = 1, activityTrackerId = 4,  sleepTrackerId = 5, cgmTrackerId = 8;
const CGM_TARGET_ID = 40;
const DEFAULT_CGM_TARGET_VALUE = 120;

const DEFAULT_VERSION = config.triggers.defaultScoreVersion;
const WINDOW_EXTENSION_DURATION_IN_HOURS = config.triggers.windowExtensionDurationInHours;

module.exports.createTriggerLog = asyncHandler(async (req, res, next) => {
  try {
    const userId = req.query?.user_guid || req.decoded.user_guid;
    let { triggeredAt, mealLogId, mealTags, imagePath } = req.body;

    if (!userId) {
      return res.status(400).json({ success: false, message: "userId missing in request", });
    }

    // Check if any device is connected which can measure CGM
    const { isEnable: isDeviceConnected } = await devicesService.isEnableTrackerForUser(userId, [cgmTrackerId]);
    if(!isDeviceConnected) {
      return next({ message: `No CGM measuring device connected`, statusCode: 400, });
    }

    triggeredAt = new Date(triggeredAt).toISOString();
    if (triggeredAt == "Invalid Date") {
      return next({ message: `Invalid triggeredAt date`, statusCode: 400, });
    }

    let expiresAt = new Date(triggeredAt);
    expiresAt.setHours(expiresAt.getHours() + WINDOW_EXTENSION_DURATION_IN_HOURS);

    triggeredAt = new Date(triggeredAt).toISOString();
    const triggers = [{ triggeredAt, mealLogId, mealTags, imagePath }];

    const newDoc = {
      userId,
      triggers,
      startedAt: triggeredAt,
      expiresAt: new Date(expiresAt).toISOString(),
      scoreComputed: false,
    };

    const valid = validate(newDoc);

    if (!valid) {
      return next({
        message: `Validation failed: ${JSON.stringify(validate.errors)}`,
        statusCode: 400,
      });
    }

    const insertedId = await triggerService.createTrigger(newDoc);
    if (!insertedId) {
      return next({ message: "Failed to save triggers log", statusCode: 500 });
    }

    return res.status(200).json({
      success: true,
      message: `Trigger Log entry saved`,
      data: { insertedId },
    });
  } catch (error) {
    logger.error(`Triggers | createTriggerLog error: ${JSON.stringify(error)}`);
    return next({ message: error?.message || "Failed to create trigger log", statusCode: 500 });
  }
});

module.exports.getAllTriggers = asyncHandler(async (req, res, next) => {
  try {
    const userId = req.query?.user_guid || req.decoded.user_guid;
    const tag = req.query?.tag;

    if (!userId) {
      return next({ message: "userId or user_guid is missing in request", statusCode: 400 });
    }

    let startTime = new Date(), endTime = new Date();
    if (req.query?.startTime && req.query?.endTime) {
      try {
        startTime = new Date(req.query?.startTime).toISOString();
        endTime = new Date(req.query?.endTime).toISOString();
      } catch (e) {
        return next({ message: "Invalid startTime or endTime", statusCode: 400 });
      }
    }
    else {
      const UTCOffsetMin = await getUTCOffsetValue(userId);
      const timeRange = getDayTimeRange(UTCOffsetMin);
      startTime = timeRange.startTime;
      endTime = timeRange.endTime;
    }

    let triggerLogs, message;

    if (tag) {
      if (!tag?.trim()) {
        return next({ message: "Invalid tag", statusCode: 400 });
      }
      triggerLogs = await triggerService.getTriggersByTag(userId, startTime, endTime, tag);
      message = `All trigger logs having "${tag}" tag for user: ${userId} for duration: ${Math.ceil((new Date(endTime) - new Date(startTime)) / (1000 * 60 * 60 * 24))}`;
    } else {
      triggerLogs = await triggerService.getAllTriggers(userId, startTime, endTime);
      message = `All trigger logs for user: ${userId} for duration: ${Math.ceil((new Date(endTime) - new Date(startTime)) / (1000 * 60 * 60 * 24))}`;
    }

    const data = [];
    const mealLogImageUrl = (await trackersService.getTrackerDetailsById(mealLogTrackerId))?.details?.media?.enabledImageUrl;
    const activityImageUrl = (await trackersService.getTrackerDetailsById(activityTrackerId))?.details?.media?.enabledImageUrl;
    const sleepImageUrl = (await trackersService.getTrackerDetailsById(sleepTrackerId))?.details?.media?.enabledImageUrl;

    // Get CGM target value
    const cgmTarget = await targetService.getLatestTargetById(userId, CGM_TARGET_ID);
    const cgmTargetValue = cgmTarget?.value || DEFAULT_CGM_TARGET_VALUE;

    // Process each trigger log
    for (const triggerLog of triggerLogs) {
      const { startedAt, expiresAt, id, triggers, activities, scoreComputed } = triggerLog;

      let cgmDetails;
      // Fetch existing score from database
      const existingScore = await triggerService.getScoreByTriggerId(userId, id);
      if (!scoreComputed || !existingScore) {
        // Get CGM data for this trigger window and calculate score
        const cgmDataStartTime = new Date(new Date(startedAt) - BASELINE_DURATION * 60000).toISOString();
        const cgmData = await logsService.getAllLogsByDateRange(config.INDEX.egvs, userId, cgmDataStartTime, expiresAt);

        // Calculate score and CGM details
        const mealStartTime = new Date(startedAt);
        const scoreObj = getCGMScoreDetails(cgmData, mealStartTime, cgmTargetValue);
        cgmDetails = generateCGMDetailsResponse(scoreObj, triggers, config.triggers);

        // Store CGM score in OpenSearch DB
        const scoreDoc = getScoreDoc(userId, triggerLog.id, scoreObj, startedAt, expiresAt, DEFAULT_VERSION);
        const scoreDocId = await triggerService.upsertScore(userId, scoreDoc);

        // Update scoreComputed flag to true
        if(scoreDocId) {
          await triggerService.updateTrigger(triggerLog.id, { scoreComputed: true });
        }
      } else {
        cgmDetails = generateCGMDetailsResponse(existingScore.metrics, triggers, config.triggers);
      }

      // Format triggers with graphIconURL
      const formattedTriggers = triggers
      ? await Promise.all(triggers.map(async ({ imagePath, ...rest }) => ({
          ...rest,
          imageUrl: await getSignedURL(imagePath),
          graphIconURL: mealLogImageUrl
        })))
      : [];
    
      // Format activities if exists
      let formattedMetadata = [];
      if (activities && activities.length > 0) {
        formattedMetadata = activities.map(meta => ({
          ...meta,
          graphIconURL: activityImageUrl
        }));
      }

      const sleepLogs = await logsService.getAllLogsByDateRange(config.INDEX.sleep, userId, startedAt, expiresAt, 'startTime');
      sleepLogs.forEach((log) => {
        formattedMetadata.push({
          eventId: log.id,
          eventType: "sleep",
          timestamp: log.startTime,
          duration: log.duration,
          details: {
            deep: log?.levels?.summary?.deep?.seconds || 0,
            rem: log?.levels?.summary?.rem?.seconds || 0,
            wake: log?.levels?.summary?.wake?.seconds || 0,
            light: log?.levels?.summary?.light?.seconds || 0
          },
          graphIconURL: sleepImageUrl    
        })
      });

      const triggerBucket = {
        startedAt,
        triggers: formattedTriggers,
        details: cgmDetails,
        expiresAt,
        id
      };

      // Add activities if it exists
      if (formattedMetadata.length > 0) {
        triggerBucket.activities = formattedMetadata;
      }

      data.push(triggerBucket);
    }

    return res.status(200).json({ success: true, message, data });
  } catch (error) {
    logger.error(`Triggers | getAllTriggers error: ${JSON.stringify(error)}`);
    return next({ message: error?.message || "Failed to get all triggers", statusCode: 500 });
  }
});

module.exports.getTriggerById = asyncHandler(async (req, res, next) => {
  try {
    const userId = req.query?.user_guid || req.decoded.user_guid;
    const triggerId = req.params.triggerId;

    if (!userId || !triggerId) {
      return res.status(400).json({
        success: false,
        message: "userId or triggerId missing in request",
      });
    }

    const triggerLog = await triggerService.getTriggerById(userId, triggerId);
    if (!triggerLog) {
      return next({ message: "Invalid triggerId or you don't have permission", statusCode: 403 });
    }

    const mealLogImageUrl = (await trackersService.getTrackerDetailsById(mealLogTrackerId))?.details?.media?.enabledImageUrl;
    const activityImageUrl = (await trackersService.getTrackerDetailsById(activityTrackerId))?.details?.media?.enabledImageUrl;
    const sleepImageUrl = (await trackersService.getTrackerDetailsById(sleepTrackerId))?.details?.media?.enabledImageUrl;

    const { startedAt, expiresAt, triggers, activities, scoreComputed } = triggerLog;

    let cgmDetails;
    // Fetch existing score from database
    const existingScore = await triggerService.getScoreByTriggerId(userId, triggerId);
    if (!scoreComputed || !existingScore) {
      // Get CGM data for this trigger window and calculate score
      const cgmDataStartTime = new Date(new Date(startedAt) - BASELINE_DURATION * 60000).toISOString();
      const cgmData = await logsService.getAllLogsByDateRange(config.INDEX.egvs, userId, cgmDataStartTime, expiresAt);

      // Calculate score and CGM details
      const mealStartTime = new Date(startedAt);
      const cgmTarget = await targetService.getLatestTargetById(userId, CGM_TARGET_ID);
      const cgmTargetValue = cgmTarget?.value || DEFAULT_CGM_TARGET_VALUE;
      const scoreObj = getCGMScoreDetails(cgmData, mealStartTime, cgmTargetValue);
      cgmDetails = generateCGMDetailsResponse(scoreObj, triggers, config.triggers);

      // Store CGM score in OpenSearch DB
      const scoreDoc = getScoreDoc(userId, triggerId, scoreObj, startedAt, expiresAt, DEFAULT_VERSION);
      const scoreDocId = await triggerService.upsertScore(userId, scoreDoc);

      // Update scoreComputed flag to true
      if(scoreDocId) {
        await triggerService.updateTrigger(triggerId, { scoreComputed: true });
      }
    } else {
      cgmDetails = generateCGMDetailsResponse(existingScore.metrics, triggers, config.triggers);
    }

    // Format triggers with graphIconURL
    const formattedTriggers = triggers
    ? await Promise.all(triggers.map(async ({ imagePath, ...rest }) => ({
        ...rest,
        imageUrl: await getSignedURL(imagePath),
        graphIconURL: mealLogImageUrl
      })))
    : [];
  
    // Format activities if exists
    let formattedMetadata = [];
    if (activities && activities.length > 0) {
      formattedMetadata = activities.map(meta => ({
        ...meta,
        graphIconURL: activityImageUrl
      }));
    }

    const sleepLogs = await logsService.getAllLogsByDateRange(config.INDEX.sleep, userId, startedAt, expiresAt, 'startTime');
    sleepLogs.forEach((log) => {
      formattedMetadata.push({
        eventId: log.id,
        eventType: "sleep",
        timestamp: log.startTime,
        duration: log.duration,
        details: {
          deep: log?.levels?.summary?.deep?.seconds || 0,
          rem: log?.levels?.summary?.rem?.seconds || 0,
          wake: log?.levels?.summary?.wake?.seconds || 0,
          light: log?.levels?.summary?.light?.seconds || 0
        },
        graphIconURL: sleepImageUrl    
      })
    });

    const data = {
      startedAt,
      triggers: formattedTriggers,
      details: cgmDetails,
      expiresAt,
      id: triggerId
    };

    // Add activities if it exists
    if (formattedMetadata.length > 0) {
      data.activities = formattedMetadata;
    }

    return res.status(200).json({
      success: true,
      message: `Found trigger log id: ${triggerId}`,
      data,
    });
  } catch (error) {
    logger.error(`Triggers | getTriggerById error: ${JSON.stringify(error)}`);
    return next({ message: error?.message || "Failed to get trigger by ID", statusCode: 500 });
  }
});

module.exports.updateTrigger = asyncHandler(async (req, res, next) => {
  try {
    const userId = req.query?.user_guid || req.decoded.user_guid;
    const { triggerId } = req.params;
    const { triggeredAt, mealLogId, mealTags } = req.body;

    if (!userId) {
      return res.status(400).json({success: false, message: "userId missing in request" });
    }

    // Check if any device is connected which can measure CGM
    const { isEnable: isDeviceConnected } = await devicesService.isEnableTrackerForUser(userId, [cgmTrackerId]);
    if(!isDeviceConnected) {
      return next({ message: `No CGM measuring device connected`, statusCode: 400, });
    }

    if (new Date(triggeredAt) === "Invalid Date") {
      return res.status(400).json({success: false, message: "Invalid triggeredAt date-time" });
    }

    if (!triggerId) {
      return res.status(400).json({success: false, message: "triggerId is required" });
    }

    const triggerLog = await triggerService.getTriggerById(userId, triggerId);

    if (triggerLog) {
      const { triggers: existingTriggers, startedAt, expiresAt } = triggerLog;
      const triggeredAt = new Date(req.body.triggeredAt);
      if (triggeredAt >= new Date(startedAt) && triggeredAt <= new Date(expiresAt)) {
        let newExpiresAt = triggerLog.expiresAt;
        const existingMealLog = existingTriggers.find(trigger => trigger.mealLogId == mealLogId);
        // expiresAt will be updated only when new MealLog entry is added, not when existing MealLog is updated
        if(!existingMealLog){
          newExpiresAt = new Date(triggeredAt);
          newExpiresAt.setHours(newExpiresAt.getHours() + WINDOW_EXTENSION_DURATION_IN_HOURS);
          newExpiresAt = newExpiresAt.toISOString();
        }
        const triggersWithoutGivenMealLogId = existingTriggers.filter(trigger => trigger.mealLogId !== mealLogId); // Deleting older mealLogId object if present
        const updatedTriggers = [
          ...triggersWithoutGivenMealLogId,
          { mealLogId, triggeredAt: triggeredAt.toISOString(), mealTags },
        ];
        await triggerService.updateTrigger(triggerId, {
          triggers: updatedTriggers,
          expiresAt: newExpiresAt,
          scoreComputed: false, // Reset score computation flag when triggers are updated
        });
        return res.status(200).json({success: true, message: `Updated trigger log: ${triggerId} for user: ${userId}` });
      } else {
        return next({ message: `Invalid triggered At`, statusCode: 400 });
      }
    } else {
      return next({ message: `No trigger log found for user: ${userId}`, statusCode: 400, });
    }
  } catch (error) {
    logger.error(`Triggers | updateTrigger error: ${JSON.stringify(error)}`);
    return next({ message: error?.message || "Failed to update trigger", statusCode: 500 });
  }
});

module.exports.upsertTrigger = asyncHandler(async (req, res, next) => {
  try {
    const userId = req.query?.user_guid || req.decoded.user_guid;
    const { triggeredAt, mealLogId, mealTags, imagePath } = req.body;

    if (!userId) {
      return res.status(400).json({success: false, message: "userId missing in request" });
    }

    // Check if any device is connected which can measure CGM
    const { isEnable: isDeviceConnected } = await devicesService.isEnableTrackerForUser(userId, [cgmTrackerId]);
    if(!isDeviceConnected) {
      return next({ message: `No CGM measuring device connected`, statusCode: 400, });
    }

    const triggeredTime = new Date(triggeredAt);
    if (triggeredTime == "Invalid Date") return res.status(400).json({ success: false, message: "Invalid triggeredAt date-time" });

    const newStart = triggeredTime;
    const newEnd = new Date(newStart);
    newEnd.setHours(newEnd.getHours() + WINDOW_EXTENSION_DURATION_IN_HOURS);

    const overlappingTriggers = await triggerService.getOverlappingTriggers(userId, newStart.toISOString(), newEnd.toISOString(), WINDOW_EXTENSION_DURATION_IN_HOURS);

    if (overlappingTriggers.length > 0) {
      let minStartedAt = newStart;
      let maxExpiresAt = newEnd;
      let mergedTriggers = [{ triggeredAt: newStart.toISOString(), mealLogId, mealTags, imagePath }];
      let mergedActivities = [];

      for (const t of overlappingTriggers) {
        const tStart = new Date(t.startedAt);
        const tEnd = new Date(t.expiresAt);
        if (tStart < minStartedAt) minStartedAt = tStart;
        if (tEnd > maxExpiresAt) maxExpiresAt = tEnd;
        mergedTriggers = mergedTriggers.concat(t.triggers || []);
        mergedActivities = mergedActivities.concat(t.activities || []);
      }

      const oldIds = overlappingTriggers.map(t => t.id);
      await triggerService.bulkDeleteTriggersByIds(userId, oldIds);

      const mergedDoc = {
        userId,
        startedAt: minStartedAt.toISOString(),
        expiresAt: maxExpiresAt.toISOString(),
        triggers: mergedTriggers,
        activities: mergedActivities,
        scoreComputed: false
      };

      const newId = await triggerService.createTrigger(mergedDoc);
      return res.status(200).json({ success: true, message: "Trigger operation completed successfully", data: { insertedId: newId } });
    } else {
      req.body.triggeredAt = newStart.toISOString();
      return this.createTriggerLog(req, res, next);
    }
  } catch (error) {
    logger.error(`Triggers | upsertTrigger error: ${JSON.stringify(error)}`);
    return next({ message: error?.message || "Failed to upsert trigger", statusCode: 500 });
  }
});

module.exports.createEvent = asyncHandler(async (req, res, next) => {
  try {
    const userId = req.query?.user_guid || req.decoded.user_guid;
    let triggerId = req.params.triggerId;
    const eventsData = req.body;

    if (!userId) {
      return res.status(400).json({ success: false, message: "userId or triggerId missing in request" });
    }

    // Check if any device is connected which can measure CGM
    const { isEnable: isDeviceConnected } = await devicesService.isEnableTrackerForUser(userId, [cgmTrackerId]);
    if(!isDeviceConnected) {
      return next({ message: `No CGM measuring device connected`, statusCode: 400, });
    }

    let triggerLog;
    if(triggerId) {
      triggerLog = await triggerService.getTriggerById(userId, triggerId);
    }
    else {
      // Find trigger based on the first event's timestamp
      if (eventsData.length > 0) {
        const firstEventTimestamp = eventsData[0].timestamp;
        triggerLog = await triggerService.getTriggerByTimestamp(userId, firstEventTimestamp);
        triggerId = triggerLog?.id || null;
      }
    }

    for (let i = 0; i < eventsData.length; i++) {
      const { eventId, eventType, timestamp } = eventsData[i];
      if (!eventId || !eventType || !timestamp || new Date(timestamp) == "Invalid Date") {
        return next({ message: "Invalid eventId or eventType or timestamp", statusCode: 400 });
      }
    }

    let activities;
    if (triggerLog) {
      if (!triggerLog.activities) {
        activities = eventsData;
      } else {
        activities = [ ...triggerLog.activities, ...eventsData ];
      }

      await triggerService.updateTrigger(triggerId, { activities });
      return res.status(200).json({success: true, message: `Updated trigger log: ${triggerId} for user: ${userId}`});
    } else {
      const errorMessage = triggerId
        ? `No trigger log found with ID ${triggerId} for user: ${userId}`
        : `No trigger log found that contains the event timestamp for user: ${userId}`;
      return next({ message: errorMessage, statusCode: 400, });
    }
  } catch (error) {
    logger.error(`Triggers | createEvent error: ${JSON.stringify(error)}`);
    return next({ message: error?.message || "Failed to create event", statusCode: 500 });
  }
});

function getScoreDoc(userId, triggerId, scoreObj, startedAt, expiresAt, version = DEFAULT_VERSION) {
  const doc = {
    userId,
    triggerId,
    startedAt,
    expiresAt,
    metrics: scoreObj,
    score: scoreObj.score.final,
    computedAt: new Date().toISOString(),
    version,
  };
  return doc;
}
