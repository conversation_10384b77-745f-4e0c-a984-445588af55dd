const { roundOffNumber } = require("./helpers");

// ===== CONFIGURABLE PARAMETERS =====
const RECOVERY_THRESHOLD_PERCENT = 10;

const BASELINE_DURATION = 30; // in mins
const MAX_SPIKE_DELTA = 30;
const MAX_AUC_ABOVE_BASELINE = 6000;
const MAX_RECOVERY_TIME_MIN = 120;
const MAX_CV_PERCENT = 36;

const WEIGHT_DELTA = 30;
const WEIGHT_AUC = 30;
const WEIGHT_RECOVERY = 25;
const WEIGHT_CV = 15;

function getTimeDiffInMinutes(later, earlier) {
  return roundOffNumber((later.getTime() - earlier.getTime()) / 60000);
}

function preprocessGlucose(glucoseData) {
  return glucoseData
    .map(entry => ({
      timestamp: new Date(entry.timestamp),
      glucose: entry.value
    }))
    .sort((a, b) => a.timestamp - b.timestamp);
}

function calculateBaseline(glucoseData, mealStartTime) {
  const baseLineDurationInSecs = BASELINE_DURATION * 60 * 1000;
  const baseLineStart = new Date(mealStartTime.getTime() - baseLineDurationInSecs);

  const preMealReadings = glucoseData.filter(({ timestamp }) => {
    const ts = new Date(timestamp);
    return ts >= baseLineStart && ts < mealStartTime;
  });

  if (preMealReadings.length === 0) return null;

  const total = preMealReadings.reduce((sum, pt) => sum + pt.glucose, 0);
  return roundOffNumber(total / preMealReadings.length);
}

function calculateAUC(glucoseData) {
  let auc = 0;
  for (let i = 1; i < glucoseData.length; i++) {
    const { glucose: g1, timestamp: t1 } = glucoseData[i - 1];
    const { glucose: g2, timestamp: t2 } = glucoseData[i];
    const duration = getTimeDiffInMinutes(t2, t1);
    auc += ((g1 + g2) / 2) * duration;
  }
  return roundOffNumber(auc);
}

function calculateRecoveryTime(glucoseData, baseline, mealStartTime) {
  const threshold = baseline * (1 + RECOVERY_THRESHOLD_PERCENT / 100);
  for (let i = glucoseData.length - 1; i >= 0; i--) {
    const reading = glucoseData[i];
    if (reading.glucose <= threshold) {
      const minutesAfterMeal = getTimeDiffInMinutes(reading.timestamp, mealStartTime);
      if (minutesAfterMeal > 0) return minutesAfterMeal;
    }
  }
  return getTimeDiffInMinutes(glucoseData[glucoseData.length - 1].timestamp, mealStartTime);
}

function calculateCoefficientOfVariation(glucoseData) {
  const values = glucoseData.map(pt => pt.glucose);
  const mean = values.reduce((a, b) => a + b, 0) / values.length;
  const stdDev = Math.sqrt(values.reduce((sum, val) => sum + (val - mean) ** 2, 0) / values.length);
  return roundOffNumber((stdDev / mean) * 100);
}

function scoreGlucoseResponse({ delta, aucAboveBaseline, recoveryTime, cv }) {
  const deltaScore = WEIGHT_DELTA * Math.max(0, 1 - delta / MAX_SPIKE_DELTA);
  const aucScore = WEIGHT_AUC * Math.max(0, 1 - aucAboveBaseline / MAX_AUC_ABOVE_BASELINE);
  const recoveryScore = WEIGHT_RECOVERY * Math.max(0, 1 - recoveryTime / MAX_RECOVERY_TIME_MIN);
  const cvScore = WEIGHT_CV * Math.max(0, 1 - cv / MAX_CV_PERCENT);
  const totalScore = deltaScore + aucScore + recoveryScore + cvScore;

  return {
    final: roundOffNumber(+(totalScore / 10)),
    total: roundOffNumber(+totalScore),
    breakdown: {
      delta: roundOffNumber(+deltaScore),
      auc: roundOffNumber(+aucScore),
      recovery: roundOffNumber(+recoveryScore),
      cv: roundOffNumber(+cvScore),
    },
  };
}

function getCGMScoreDetails(glucoseData, mealStartTime, targetThreshold) {
  const processedData = preprocessGlucose(glucoseData);
  if (!processedData.length) return zeroScore();

  const baseline = calculateBaseline(processedData, mealStartTime);
  if (!baseline) return zeroScore();

  const maxGlucose = Math.max(...processedData.map(pt => pt.glucose));
  const delta = maxGlucose - baseline;
  const aucTotal = calculateAUC(processedData);
  const totalDuration = getTimeDiffInMinutes(processedData.at(-1).timestamp, processedData[0].timestamp);
  const aucAboveBaseline = aucTotal - baseline * totalDuration;
  const recoveryTime = calculateRecoveryTime(processedData, baseline, mealStartTime);
  const cv = calculateCoefficientOfVariation(processedData);
  const timeAboveTarget = calculateTimeAboveTarget(processedData, targetThreshold);

  const score = scoreGlucoseResponse({ delta, aucAboveBaseline, recoveryTime, cv });

  return {
    score,
    baseline: roundOffNumber(+baseline),
    peakCGM: roundOffNumber(+maxGlucose),
    glucoseDelta: roundOffNumber(delta),
    timeAboveTarget: roundOffNumber(+timeAboveTarget),
    aucAboveBaseline: roundOffNumber(+aucAboveBaseline),
    recoveryTime: roundOffNumber(+recoveryTime),
    cv: roundOffNumber(+cv),
    totalDuration: roundOffNumber(+totalDuration),
    aucTotal: roundOffNumber(+aucTotal)
  };
}

function calculateTimeAboveTarget(glucoseData, targetThreshold = 140) {
  const processedData = preprocessGlucose(glucoseData);
  let aboveTargetMinutes = 0;

  for (let i = 1; i < processedData.length; i++) {
    const prev = processedData[i - 1], curr = processedData[i];
    const diffMin = (curr.timestamp - prev.timestamp) / 60000;
    if (curr.glucose > targetThreshold || prev.glucose > targetThreshold) {
      aboveTargetMinutes += diffMin;
    }
  }

  return Math.round(aboveTargetMinutes);
}

function calculateTotalCaloriesFromMeals(triggers = []) {
  let total = 0;
  for (const t of triggers) {
    for (const tag of t.mealTags || []) {
      total += tag.computedNutritions?.calories?.quantity || 0;
    }
  }
  return total;
}

function generateCGMDetailsResponse(scoreObj, triggers, options = {}) {
  const title = options.title;
  const description = options.description;
  const totalCalories = calculateTotalCaloriesFromMeals(triggers);

  return {
    peakCGM: { value: scoreObj.peakCGM, unit: "mg/dl" },
    timeInRange: { value: scoreObj.timeAboveTarget, unit: "%" },
    score: {
      final: +(scoreObj.score.final || 0),
      total: +(scoreObj.score.total || 0),
      breakdown: {
        delta: +(scoreObj.score.breakdown?.delta || 0),
        auc: +(scoreObj.score.breakdown?.auc || 0),
        recovery: +(scoreObj.score.breakdown?.recovery || 0),
        cv: +(scoreObj.score.breakdown?.cv || 0)
      }
    },
    glucoseDelta: { value: scoreObj.glucoseDelta, unit: "mg/dl" },
    totalCalories: { value: totalCalories, unit: "kcal" },
    content: { title, description }
  };
}

function zeroScore() {
  return {
    score: {
      final: 0,
      total: 0,
      breakdown: { delta: 0, auc: 0, recovery: 0, cv: 0 }
    },
    baseline: 0,
    peakCGM: 0,
    glucoseDelta: 0,
    timeAboveTarget: 0,
    aucAboveBaseline: 0,
    recoveryTime: 0,
    cv: 0,
    totalDuration: 0,
    aucTotal: 0,
  };
}

module.exports = {
  getCGMScoreDetails,
  generateCGMDetailsResponse,
  BASELINE_DURATION,
};
