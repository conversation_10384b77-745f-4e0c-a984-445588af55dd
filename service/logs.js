const { getClient } = require("../utils/connection");
const { log: logger } = require("../utils/logger");
const { trackersService } = require("./trackers");
const { getLocalDateString } = require("../utils/helpers");
const { sendTargetComputationSQSMessage } = require("../utils/aws");
const { getUTCOffsetValue } = require("../service/userProfile");
const exerciseLogs = require("../utils/exerciseLogs");
const mindfulnessLogs = require("../utils/mindfulnessLogs");
const { config } = require("../environment/index");
const MANUAL_ENTRY_DEVICE_ID = -1;
const Ajv = require("ajv");
const addFormats = require("ajv-formats");
const ajv = new Ajv({ allErrors: true });
addFormats(ajv);

const models = require("../models");

const trackerMap = {
  1: {  
    // meal log: all the operations like meallog CRUD & target computation will happen in meal log service
    indexName: null,
    validate: ajv.compile(models[1]),
    filterParam: 'createdAt',
    aggregation: true
  },
  2: {
    indexName: config.INDEX.water,
    validate: ajv.compile(models[2]),
    filterParam: 'timestamp',
    aggregation: true
  },
  3: {
    indexName: config.INDEX.activitySummary,
    validate: ajv.compile(models[3]),
    filterParam: 'date',
    aggregation: true
  },
  4: {
    indexName: config.INDEX.activity,
    validate: ajv.compile(models[4]),
    filterParam: 'timestamp',
    aggregation: true
  },
  5: {
    indexName: config.INDEX.sleep,
    validate: ajv.compile(models[5]),
    filterParam: 'timestamp',
    aggregation: true
  },
  6: {
    indexName: config.INDEX.bp,
    validate: ajv.compile(models[6]),
    filterParam: 'timestamp',
    aggregation: false
  },
  7: {
    indexName: config.INDEX.bg,
    validate: ajv.compile(models[7]),
    filterParam: 'timestamp',
    aggregation: false
  },
  8: {
    indexName: config.INDEX.egvs,
    validate: ajv.compile(models[8]),
    filterParam: 'timestamp',
    aggregation: false
  },
  9: {
    indexName: config.INDEX.spo2,
    validate: ajv.compile(models[9]),
    filterParam: 'date',
    aggregation: false
  },
  10: {
    indexName: config.INDEX.heartRate,
    validate: ajv.compile(models[10]),
    filterParam: 'date',
    aggregation: false
  },
  11: {
    indexName: config.INDEX.hrv,
    validate: ajv.compile(models[11]),
    filterParam: 'date',
    aggregation: false
  },
  12: {
    indexName: config.INDEX.vo2,
    validate: ajv.compile(models[12]),
    filterParam: 'date',
    aggregation: false
  },
  13: {
    indexName: config.INDEX.ecg,
    validate: ajv.compile(models[13]),
    filterParam: 'timestamp',
    aggregation: false
  },
  14: {
    indexName: config.INDEX.height,
    validate: ajv.compile(models[14]),
    filterParam: 'date',
    aggregation: false
  },
  15: {
    indexName: config.INDEX.weight,
    validate: ajv.compile(models[15]),
    filterParam: 'date',
    aggregation: false
  },
  16: {
    indexName: config.INDEX.fat,
    validate: ajv.compile(models[16]),
    filterParam: 'date',
    aggregation: false
  },
  17: {
    indexName: config.INDEX.bmi,
    validate: ajv.compile(models[17]),
    filterParam: 'date',
    aggregation: false
  },
  18: {
    indexName: config.INDEX.temp,
    validate: ajv.compile(models[18]),
    filterParam: 'timestamp',
    aggregation: false
  },
  19: {
    indexName: config.INDEX.waistSize,
    validate: ajv.compile(models[19]),
    filterParam: 'date',
    aggregation: false
  },
  20: {
    indexName: config.INDEX.hipSize,
    validate: ajv.compile(models[20]),
    filterParam: 'date',
    aggregation: false
  },
  21: {
    indexName: config.INDEX.chestSize,
    validate: ajv.compile(models[21]),
    filterParam: 'date',
    aggregation: false
  },
  22: {
    indexName: config.INDEX.armSize,
    validate: ajv.compile(models[22]),
    filterParam: 'date',
    aggregation: false
  },
  23: {
    indexName: config.INDEX.quadSize,
    validate: ajv.compile(models[23]),
    filterParam: 'date',
    aggregation: false
  },
  24: {
    indexName: config.INDEX.mindfulness,
    validate: ajv.compile(models[24]),
    filterParam: 'date',
    aggregation: true
  },
  25: {
    indexName: config.INDEX.restingHeartRate,
    validate: ajv.compile(models[25]),
    filterParam: 'date',
    aggregation: false
  }
};

async function getDataByLogId(indexName, userId, logId) {
  const client = await getClient();
  const response = await client.search({
    index: indexName,
    body: {
      query: {
        bool: {
          must: [{ match: { _id: logId } }, { match: { "userId.keyword": userId } }],
        },
      },
    },
  });

  const data = response.body?.hits?.hits[0] || null
  return data ? { id: data._id, ...data._source } : null
}

async function getAllLogsByDateRange(indexName, userId, startTime, endTime, timeRangeParam = 'timestamp', deviceId = null) {
  const client = await getClient();
  let query = {
    bool: {
      must: [
        { match: { "userId.keyword": userId } },
        { range: { [timeRangeParam]: { gte: startTime, lte: endTime } } },
      ],
    },
  };

  if (deviceId) {
    query.bool.must.push({ term: { deviceId } }); // Use `term` for exact matches on `deviceId`
  }

  const response = await client.search({
    index: indexName,
    body: {
      _source: { excludes: ["userId"] },
      sort: [{ [timeRangeParam]: {order: "desc"} }],
      size: 2500,
      query,
    },
  });

  if (response.body?.hits) {
    const data = response.body?.hits?.hits?.map((itm) => {
      return { id: itm._id, ...itm._source }
    });
    return data;
  }
  return [];
}

async function getAllLogsByPagination(indexName, userId, from, size) {
  const client = await getClient();

  const response = await client.search({
    index: indexName,
    body: {
      _source: { excludes: ["userId"] },
      sort: [{ timestamp: {order: "desc"} }],
      from, size,
      query: { match: { "userId.keyword": userId } },
    },
  });

  if (response.body?.hits) {
    const data = response.body?.hits?.hits?.map((itm) => {
      return { id: itm._id, ...itm._source }
    });
    return data;
  }
  return [];
}

async function deleteLogById(indexName, userId, logId) {
  const log = await getDataByLogId(indexName, userId, logId);
  const client = await getClient();
  if (log) {
    const deletedLog = await client.delete({
      index: indexName,
      id: logId,
    });

    delete log?.id;
    return log;
  }
}

async function updateLogById(trackerId, indexName, document, userId, logId) {
  try {
    const client = await getClient();
    const data = await getDataByLogId(indexName, userId, logId);

    if (data) {
      const updatedLog = await client.update({
        index: indexName, id: logId,
        body: { doc: { ...document } }
      });
      logger.debug({ message: "Updated log", document });
      // Log update & then target computation will happen in respective repos
      switch(Number(trackerId)){
        case 4: // exercise_logs
          await exerciseLogs.addLog(document.userId, document); 
          break;
        case 24: // mindfulness_logs  
          await mindfulnessLogs.addLog(document.userId, document);
          break;
        default:
          delete data?.id;  
          break;
      }   
      return updatedLog.body?._id;
    }
  } catch (error) {
    logger.warn("Failed to upsert log", JSON.stringify(error));
  }
}

/**
 * logs will be array of logs in same format as of db
 */
async function postLogUpsert(userId, trackerIdLogsMapping) {
  try {
    const defaultTrackers = await trackersService.getAllDefaultTrackers(userId);
    const UTCOffsetMin = await getUTCOffsetValue(userId);

    const trackerIdDeviceId = {};
    for (const trackerId in trackerIdLogsMapping) {
      const logs = trackerIdLogsMapping[trackerId];
      for (const log of logs) {
        const date = getLocalDateString(log.timestamp, UTCOffsetMin);
        let computeTargetAchievementFlag = checkDeviceConditions(log.deviceId, trackerId, defaultTrackers);
        if (computeTargetAchievementFlag) {
          if (!trackerIdDeviceId[date]) {
            trackerIdDeviceId[date] = { [trackerId]: log.deviceId };
          } else {
            trackerIdDeviceId[date][trackerId] = log.deviceId;
          }
        }
      }
    }
       
    logger.info(`postLogUpsert for userId: ${userId}, trackerIdDeviceId: ${JSON.stringify(trackerIdDeviceId)}`);

    const promises = Object.keys(trackerIdDeviceId).map(async (date) => {
      const message = JSON.stringify({
        userId,
        date,
        trackerIdDeviceIdMapping: trackerIdDeviceId[date],
        time: new Date().getTime(), // unique identifier
      });
      try {
        const isSent = await sendTargetComputationSQSMessage(message);
        return { date, success: isSent };
      } catch (err) {
        logger.warn(`Error sending SQS message for date: ${date} | ${JSON.stringify(err)}`);
        return { date, success: false };
      }
    });

    const data = await Promise.all(promises);
    logger.info(`postLogUpsert response data: ${JSON.stringify(data)}`);
    return true;
  } catch (error) {
    logger.warn(`Error in postLogUpsert | userId: ${userId}, Error: ${JSON.stringify(error)}`);
    return false;
  }
}

function checkDeviceConditions(logDeviceId, logTrackerId, defaultTrackers) {
  let isDefaultTrackerLog = false
  if (defaultTrackers?.defaultDevices?.length) {
    const dd = defaultTrackers?.defaultDevices.filter(dd => 
      dd.trackerId == logTrackerId && dd.deviceId == logDeviceId
    )
    if (dd && dd.length) isDefaultTrackerLog = true
  }

  // Check if only Manual Entry (no default device set)
  const isManualLog = logDeviceId == MANUAL_ENTRY_DEVICE_ID;
  let isOnlyManualDevice = logDeviceId == MANUAL_ENTRY_DEVICE_ID;
  if (defaultTrackers?.defaultDevices?.length) {
    const dd = defaultTrackers?.defaultDevices.filter(dd => 
      dd.trackerId == logTrackerId
    )
    if (dd && dd.length) isOnlyManualDevice = false
  }  

  return (isDefaultTrackerLog || isManualLog);
}

module.exports = {
  trackerMap,
  postLogUpsert,
  getDataByLogId,
  getAllLogsByDateRange,
  getAllLogsByPagination,
  updateLogById,
  deleteLogById,
};
